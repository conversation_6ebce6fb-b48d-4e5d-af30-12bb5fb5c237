import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Col,
    Input,
    List,
    message,
    Modal,
    Progress,
    Row,
    Spin,
    Table,
    Tag,
    Typography,
} from 'antd';
import * as XLSX from 'xlsx';
import FormBuilder from 'antd-form-builder';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import React, { useState, useEffect, useCallback } from 'react';
import {
    convertDateFieldsToMoments,
    decodeAntdFormErrorsToString,
    ExcelDateToJSDate,
} from '../../util/helpers';
import moment from 'moment';
import { Form } from 'antd';
import { defaultStyles, FileIcon } from 'react-file-icon';
import http_utils from '../../util/http_utils';
import ReactExport from 'react-export-excel';
import {
    CheckCircleFilled,
    DownloadOutlined,
    CloseCircleFilled,
    ExclamationCircleFilled,
} from '@ant-design/icons';
import LocalStorageManager from '../../util/LocalStorageManager';
import ConfigHelpers from '../../util/ConfigHelpers';
import ruleValidator from './AntdRuleValidator';
import { BiError } from 'react-icons/bi';
import { ImCancelCircle } from 'react-icons/im';

const { Paragraph } = Typography;

const ExcelFile = ReactExport.ExcelFile;
const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;
const pageSize = 10;
const defaultMaxRows = 1000;

const dataProtoFrBulkUpload = [
    {
        label: 'Name',
        required: true,
        key: 'user_name',
    },
    {
        key: 'user_number',
        label: 'Mobile(+91)',
        required: true,
        rules: [
            {
                pattern: new RegExp('^[0-9]*$'),
                message: 'Incorrect number',
            },
            { min: 10 },
            { max: 10 },
        ],
    },
    {
        label: 'Gender',
        required: true,
        key: 'user_gender',
        widget: 'select',
        options: [
            {
                label: 'Male',
                value: 'male',
            },
            {
                label: 'Female',
                value: 'female',
            },
        ],
    },
    {
        label: 'Weight',
        required: false,
        key: 'user_weight',
        widget: 'number',
    },
    {
        label: 'DOB',
        required: false,
        key: 'user_dob',
        widget: 'date-picker',
    },
];

const SingleRowFormFrBuilder = (props) => {
    const {
        meta,
        initialValues,
        onValidationError,
        onReadyToUpload,
        renderFullForm,
    } = props;
    const [error, setError] = useState(undefined);
    const [checked, setChecked] = useState(false);

    if (!checked) {
        setChecked(true);
        setTimeout(async () => {
            const validate = await ruleValidator(
                meta.fields,
                initialValues,
                props.isBulkAssignComp
            );
            if (validate.length > 0) {
                const errorString = validate?.[0]?.errors.join(',');
                setError(errorString || 'Error');
                onValidationError(validate);
            } else {
                setError('success');
                onReadyToUpload(initialValues);
            }
        }, 10);
    }

    return !renderFullForm && error ? (
        error == 'success' ? (
            <Tag color="success">Ready</Tag>
        ) : (
            <span className="gx-text-red">{error}</span>
        )
    ) : (
        <></>
    );
};

const BulkUploaderV2 = (props) => {
    const {
        demoMode,
        debugMode,
        maxRows = defaultMaxRows,
        renderFormsForRows,
        onDataModified,
        submitUrl,
        dataProto,
        orgSettingsData,
        update,
        timeFormatMsg,
        errorHasLineBreaks,
        isBulkAssignComp,
        bulkUpdateAuthority,
        selectedSlotData,
    } = props;

    // State management
    const [state, setState] = useState({
        cols: [],
        rows: [],
        bulkUploadInProgress: false,
        bulkUploadProgress: 0,
        clearFileInput: false,
        uploadComplete: false,
        errorInfile: false,
        erroricRows: 0,
        majorErrors: [],
        readyToUploadRows: [],
        readyToUploadRowKeys: [],
        uploadResp: '',
        fileCheckingInProg: false,
        currentPage: 1,
        fileName: '',
        isExcelUploaded: false,
        multipleFieldDataModals: {},
        finalData: undefined,
    });

    // Refs for tracking upload state
    const readyToUploadRowKeysRef = React.useRef({});
    const readyToUploadRowsRef = React.useRef([]);
    const erroricRowsRef = React.useRef(0);
    const erroricRowsKeysRef = React.useRef({});
    const majorErroricRowsKeysRef = React.useRef({});

    const initFileCheckVars = (resetMajorErroricRowsKeys = true) => {
        readyToUploadRowKeysRef.current = {};
        readyToUploadRowsRef.current = [];
        erroricRowsRef.current = 0;
        erroricRowsKeysRef.current = {};
        if (resetMajorErroricRowsKeys) {
            majorErroricRowsKeysRef.current = {};
        }
    };

    useEffect(() => {
        initFileCheckVars();
    }, []);

    const showSelectFieldOptionsModal = (key) => {
        setState((prev) => ({
            ...prev,
            multipleFieldDataModals: {
                ...prev.multipleFieldDataModals,
                [key]: true,
            },
        }));
    };

    const hideSelectFieldOptionsModal = (key) => {
        setState((prev) => ({
            ...prev,
            multipleFieldDataModals: {
                ...prev.multipleFieldDataModals,
                [key]: false,
            },
        }));
    };

    const getDataProto = () => {
        console.log('dataProto==>', dataProto);
        let baseProto;

        if (demoMode && !dataProto) {
            baseProto = [...dataProtoFrBulkUpload];
        } else {
            baseProto = [...(dataProto || [])];
        }

        // Add Start/End Time as mandatory columns if time slots are passed
        if (selectedSlotData && selectedSlotData.length > 0) {
            console.log('selectedSlotData==>', selectedSlotData);
            const slotOptions = selectedSlotData.map((slot) => ({
                label: slot.timeRange, // e.g., "09:00AM-11:00AM"
                value: slot.timeRange,
            }));

            baseProto.push({
                key: 'booking_slot',
                label: 'Booking Slot',
                widget: 'select',
                required: true,
                options: slotOptions,
            });
        }
        console.log('baseProto==>', baseProto);
        return baseProto;
    };

    const getMeta = () => {
        let finalFields = [];
        let dataProtoData = getDataProto();
        dataProtoData.map((singleFieldMeta) => {
            if (singleFieldMeta.label) {
                singleFieldMeta.colSpan = 2;
                singleFieldMeta.renderView = () => <div></div>;
                finalFields.push(singleFieldMeta);
            }
        });
        const meta = {
            columns: state.rows[0]?.length || 0,
            formItemLayout: null,
            fields: finalFields,
        };
        return meta;
    };

    const getSingleColumFieldMeta = (columnText) => {
        let returnMeta = {};
        let dataProtoData = getDataProto();
        dataProtoData.map((singleFieldMeta) => {
            if (singleFieldMeta.label == columnText) {
                returnMeta = singleFieldMeta;
            }
        });
        return returnMeta;
    };

    const getUploadStateFieldIndex = () => {
        return 0;
    };

    const getData = (rowsInState = undefined) => {
        let rows = rowsInState || state.rows;
        let finalRows = [];
        rows.map((singleRow, index) => {
            // first is header
            if (index > 0) {
                singleRow.batch_data_row_key = index;
                finalRows.push(singleRow);
            }
        });
        return finalRows;
    };

    const isReadyToUpload = () => {
        if (getData()?.length > 0) {
            return true;
        }
        return false;
    };

    const getFormDataForRow = (rowData) => {
        let columns = state.rows[0];
        let formDataProtoFrRow = {};
        columns.map((columnTitle, index) => {
            let singleFieldMeta = getSingleColumFieldMeta(columnTitle);
            let value = rowData[index];
            if (singleFieldMeta.widget == 'date-picker') {
                if (value) {
                    let valueConverted = ExcelDateToJSDate(value);
                    if (valueConverted) {
                        value = moment(valueConverted);
                        value = value.format('YYYY-MM-DD');
                    } else {
                        value = moment(value);
                    }
                } else {
                    value = undefined;
                }
            } else if (
                singleFieldMeta.widget != 'select' &&
                singleFieldMeta.widget != 'number' &&
                value
            ) {
                value = '' + value;
            } else if (update && !value && singleFieldMeta.widget != 'select') {
                value = '';
            } else if (
                bulkUpdateAuthority &&
                !value &&
                singleFieldMeta.widget == 'select'
            ) {
                value = '';
            }
            if ((value || !bulkUpdateAuthority) && singleFieldMeta.options) {
                value = '' + value;
                if (
                    singleFieldMeta.widgetProps?.mode == 'multiple' &&
                    value?.split(',')?.length >= 1
                ) {
                    let finalValue = [];
                    value.split(',').map((singleValue) => {
                        let matchingEntry = singleFieldMeta.options.filter(
                            (singleOption) =>
                                singleOption.label == singleValue?.trim()
                        );
                        if (matchingEntry.length > 0) {
                            finalValue.push(matchingEntry[0].value);
                        }
                    });
                    if (finalValue.length == 0) {
                        value = undefined;
                    } else {
                        value = finalValue;
                    }
                } else {
                    let matchingEntry;
                    if (bulkUpdateAuthority) {
                        matchingEntry = singleFieldMeta.options.filter(
                            (singleOption) =>
                                singleOption.label?.trim() == value?.trim()
                        );
                    } else {
                        matchingEntry = singleFieldMeta.options.filter(
                            (singleOption) =>
                                singleOption.label == value?.trim()
                        );
                    }
                    if (matchingEntry.length == 0) {
                        value = undefined;
                    } else {
                        value = matchingEntry[0].value;
                    }
                }
            }

            formDataProtoFrRow[singleFieldMeta.key] = value;
        });
        formDataProtoFrRow = convertDateFieldsToMoments(
            formDataProtoFrRow,
            getDataProto()
        );
        return formDataProtoFrRow;
    };

    const getPostData = () => {
        let postData = state.readyToUploadRows;

        // Add booking data if available from dataProto
        if (dataProto) {
            const {
                selectedPrvdr,
                bookingMode,
                selectedWeekData,
                selectedSlotData,
                selectedDay,
            } = dataProto;

            if (
                selectedPrvdr ||
                bookingMode ||
                selectedWeekData ||
                selectedSlotData ||
                selectedDay
            ) {
                // Add booking information to each row
                postData = postData.map((row) => ({
                    ...row,
                    ...(selectedPrvdr && { bulk_booking_prvdr: selectedPrvdr }),
                    ...(bookingMode && { bulk_booking_mode: bookingMode }),
                    ...(selectedWeekData && {
                        bulk_booking_week_data: selectedWeekData,
                    }),
                    ...(selectedSlotData && {
                        bulk_booking_slot_data: selectedSlotData,
                    }),
                    ...(selectedDay && { bulk_booking_day: selectedDay }),
                }));
            }
        }
        console.log('postdata', postData);
        return postData;
    };

    const handleFailedRow = (batch_data_row_key) => {
        if (!erroricRowsKeysRef.current[batch_data_row_key]) {
            erroricRowsKeysRef.current[batch_data_row_key] = true;
            erroricRowsRef.current = erroricRowsRef.current + 1;
        }

        setState((prev) => ({
            ...prev,
            errorInfile: true,
        }));
        checkIfAllRowsScanningIsDone();
    };

    const handleReadyToUpload = (readyToUploadRow, batch_data_row_key) => {
        let readyToUploadRowKeys = readyToUploadRowKeysRef.current;
        if (!readyToUploadRowKeys[batch_data_row_key]) {
            let readyToUploadRows = readyToUploadRowsRef.current;
            readyToUploadRows.push(readyToUploadRow);
            readyToUploadRowKeys[batch_data_row_key] = true;
            readyToUploadRowKeysRef.current = readyToUploadRowKeys;
            readyToUploadRowsRef.current = readyToUploadRows;
        }

        checkIfAllRowsScanningIsDone();
    };

    const checkIfAllRowsScanningIsDone = () => {
        let numOfValidRows = readyToUploadRowsRef.current.length;
        let erroricRows = erroricRowsRef.current;
        let totalRowsScanned = numOfValidRows + erroricRows;
        let totalRows = getData().length;

        if (
            totalRowsScanned == totalRows &&
            state.readyToUploadRows.length == 0
        ) {
            setState((prev) => ({
                ...prev,
                erroricRows: erroricRows,
                readyToUploadRowKeys: readyToUploadRowKeysRef.current,
                readyToUploadRows: readyToUploadRowsRef.current,
                fileCheckingInProg: false,
                currentPage: prev.isExcelUploaded ? 1 : prev.currentPage,
                bulkUploadProgress: 0,
                isExcelUploaded: false,
            }));
        } else if (state.fileCheckingInProg) {
            let numberOfPages = Math.ceil(totalRows / 10);
            let currentPage = state.currentPage;
            if (
                currentPage <= numberOfPages &&
                totalRowsScanned == currentPage * 10
            ) {
                setState((prev) => ({
                    ...prev,
                    currentPage: currentPage + 1,
                    bulkUploadProgress: Math.round(
                        (currentPage / numberOfPages) * 100
                    ),
                }));
            }
        }
    };

    const validateDateCols = (resp) => {
        let erroricColumns = [];
        let columns = resp.rows[0];
        let onlyRows = [...resp.rows];
        delete onlyRows[0];
        onlyRows.map((singleRow, rowNo) => {
            singleRow.map((singleCellValue, index) => {
                let fieldName = columns[index];
                let fieldMeta = getSingleColumFieldMeta(fieldName);
                if (fieldMeta.widget == 'date-picker') {
                    if (singleCellValue != '') {
                        let value = ExcelDateToJSDate(singleCellValue);
                        if (value) {
                            // value is valid
                        } else {
                            erroricColumns.push(fieldMeta.label);
                            if (!majorErroricRowsKeysRef.current[rowNo]) {
                                majorErroricRowsKeysRef.current[rowNo] = true;
                            }
                        }
                    }
                }
            });
        });
        return erroricColumns;
    };

    const validatePincodeCols = (resp) => {
        let erroricColumns = [];
        let columns = resp.rows[0];
        let onlyRows = [...resp.rows];
        delete onlyRows[0];
        onlyRows.map((singleRow, rowNo) => {
            singleRow.map((singleCellValue, index) => {
                let fieldName = columns[index];
                let fieldMeta = getSingleColumFieldMeta(fieldName);
                let value = singleCellValue.toString().length;
                if (fieldMeta.key == 'cust_pincode') {
                    if (
                        singleCellValue != '' &&
                        value !=
                            (orgSettingsData?.selected_country_pincode_length ??
                                6)
                    ) {
                        erroricColumns.push(fieldMeta.label);
                        if (!majorErroricRowsKeysRef.current[rowNo]) {
                            majorErroricRowsKeysRef.current[rowNo] = true;
                        }
                    }
                }
            });
        });
        return erroricColumns;
    };

    const doBasicValidationsAndRefreshUI = (resp, fileName, startRow) => {
        let majorErrors = [];
        let dataProtoData = getDataProto();
        let currentRowsLength = resp.rows.length - 1;
        if (maxRows && currentRowsLength > maxRows) {
            majorErrors.push(
                `Total rows in excel (${currentRowsLength}) exceeds maximum allowed ${maxRows} rows`
            );
        }
        let acceptedColumns = dataProtoData.map((fieldMeta) => fieldMeta.label);
        let requiredFields = dataProtoData.filter(
            (fieldMeta) => fieldMeta.required
        );
        let columns = resp.rows[0];
        let missingReqFields = requiredFields.filter(
            (fieldMeta) => !columns.includes(fieldMeta.label)
        );
        if (missingReqFields.length > 0) {
            majorErrors.push(
                'Missing column - ' +
                    missingReqFields
                        .map((fieldMeta) => fieldMeta.label)
                        .join(',')
            );
        }
        let unRecognisedFields = columns.filter(
            (columnTitle) => !acceptedColumns.includes(columnTitle)
        );
        if (unRecognisedFields.length > 0) {
            majorErrors.push(
                ' Unrecognised column - ' + unRecognisedFields.join(',')
            );
        }
        validateDateCols(resp);
        validatePincodeCols(resp);

        let uploaded_file_name = localStorage.getItem(
            'RECENT_UPLOADED_SRVC_REQ_EXCEL'
        );
        if (fileName == uploaded_file_name) {
            majorErrors.push(
                'Duplicate excel !! This excel was already uploaded successfully'
            );
        }

        if (Object.keys(majorErroricRowsKeysRef.current).length > 0) {
            initFileCheckVars(false);
        } else {
            initFileCheckVars();
        }
        //resetAllRefs();
        if (startRow != 1) {
            resp.rows = resp.rows.slice(1);
        }

        setState((prev) => ({
            ...prev,
            cols: resp.cols,
            rows: [...prev.rows, ...resp.rows],
            errorInfile:
                majorErrors.length > 0 ||
                Object.keys(majorErroricRowsKeysRef.current).length > 0,
            majorErrors: majorErrors,
            readyToUploadRows: [],
            fileCheckingInProg: true,
            bulkUploadProgress: 1,
            fileName: fileName,
        }));
    };

    const handleFileChange = async (event) => {
        let fileObj = event.target.files[0];
        try {
            await message.loading('Loading excel...');

            const excelData = await new Promise((resolve, reject) => {
                ExcelRenderer(fileObj, (err, resp) => {
                    if (err) {
                        reject('Unable to read the excel');
                    } else {
                        resolve(resp);
                    }
                });
            });

            const header = excelData.rows[0];
            const CHUNK_SIZE = 100;
            const totalRows = excelData.rows.length;
            let startRow = 1;

            // Process the rows in chunks
            while (startRow < totalRows) {
                const chunkRows = excelData.rows.slice(
                    startRow,
                    startRow + CHUNK_SIZE
                );
                const chunkWithHeader = [header, ...chunkRows];

                const processedRows = chunkWithHeader.filter((singleRow) => {
                    if (singleRow.length > 0) {
                        singleRow = singleRow.filter(
                            (cellVal) => cellVal && cellVal !== ''
                        );
                    }
                    return singleRow.length > 0;
                });
                doBasicValidationsAndRefreshUI(
                    { rows: processedRows, cols: excelData.cols },
                    fileObj.name,
                    startRow
                );

                startRow += CHUNK_SIZE;
            }
            setState((prev) => ({ ...prev, isExcelUploaded: true }));
            message.success('Excel loaded successfully.');
        } catch (e) {
            message.error(e || 'Unable to read the excel');
            initFileCheckVars();
            setState({
                cols: [],
                rows: [],
                bulkUploadInProgress: false,
                bulkUploadProgress: 0,
                clearFileInput: false,
                uploadComplete: false,
                errorInfile: false,
                erroricRows: 0,
                majorErrors: [],
                readyToUploadRows: [],
                readyToUploadRowKeys: [],
                uploadResp: '',
                fileCheckingInProg: false,
                currentPage: 1,
                fileName: '',
                isExcelUploaded: false,
                multipleFieldDataModals: {},
            });
        }
    };

    const onStartBulkCreation = (event) => {
        setState((prev) => ({
            ...prev,
            bulkUploadInProgress: true,
            bulkUploadProgress: 50,
        }));

        let params = {};
        params['batch_data'] = getPostData();
        const onComplete = (resp) => {
            setState((prev) => ({
                ...prev,
                bulkUploadInProgress: false,
                // bulkUploadProgress: 100,
                bulkUploadProgress: 50,
                uploadComplete: true,
                uploadResp: `Uploaded successfully`,
                finalData: resp.data,
            }));
            tellParent(resp.data.entry_ids);
            LocalStorageManager.setData(
                'RECENT_UPLOADED_SRVC_REQ_EXCEL',
                state.fileName
            );
        };
        const onError = (error) => {
            let errorDecoded = http_utils.decodeErrorToMessage(error);
            if (typeof errorDecoded == 'string') {
                errorDecoded = JSON.parse(errorDecoded).hint;
            }
            setState((prev) => ({
                ...prev,
                bulkUploadInProgress: false,
                bulkUploadProgress: 100,
                uploadComplete: true,
                uploadResp: errorDecoded,
            }));
        };
        http_utils.performPostCall(
            '/services/modify_booking',
            params,
            onComplete,
            onError
        );
    };

    const tellParent = (entry_ids) => {
        if (onDataModified) {
            onDataModified(entry_ids);
        }
    };
    // Initialization
    const resetAllRefs = () => {
        erroricRowsKeysRef.current = {};
        majorErroricRowsKeysRef.current = {};
        readyToUploadRowKeysRef.current = {};
        readyToUploadRowsRef.current = [];
        erroricRowsRef.current = 0;
    };

    // Replace handleClear with this:
    const handleClear = () => {
        initFileCheckVars();
        resetAllRefs();

        // Step 1: force unmount the input
        setState((prev) => ({
            ...prev,
            clearFileInput: true,
        }));

        // Step 2: reset everything after unmount
        setTimeout(() => {
            setState({
                cols: [],
                rows: [],
                bulkUploadInProgress: false,
                bulkUploadProgress: 0,
                clearFileInput: false, // input remounts cleanly
                uploadComplete: false,
                errorInfile: false,
                erroricRows: 0,
                majorErrors: [],
                readyToUploadRows: [],
                readyToUploadRowKeys: [],
                uploadResp: '',
                fileCheckingInProg: false,
                currentPage: 1,
                fileName: '',
                isExcelUploaded: false,
                multipleFieldDataModals: {},
            });
        }, 0);
    };

    const getColMeta = () => {
        let colMeta = [
            {
                key: 'cust_mobile',
                title: 'Cutomer Mobile',
            },
            {
                key: 'cust_full_name',
                title: 'Customer name',
            },
            {
                key: 'bulk_booking_day',
                title: 'Booking day',
            },
            {
                key: 'request_priority',
                title: 'Priority',
            },
            {
                key: 'request_description',
                title: 'description',
            },
            // {
            //     key: 'day',
            //     title: 'Day',
            // },
            // {
            //     key: 'attendance',
            //     title: 'Attendance',
            // },
            // {
            //     key: 'start_time',
            //     title: 'Start time',
            // },
            // {
            //     key: 'end_time',
            //     title: 'End time',
            // },
        ];
        return colMeta;
    };

    const downloadFailedToBookRequest = () => {
        let ColMeta = getColMeta();
        // let RowMeta = getRowMeta();
        let srvcReqAttendanceData = [];

        state.finalData.failed_to_book.forEach((singleRowMeta) => {
            let dummyObj = {};
            ColMeta.forEach((singleColumns) => {
                dummyObj[singleColumns.title] = singleRowMeta[singleColumns.key]
                    ? singleRowMeta[singleColumns.key]
                    : '';
            });
            srvcReqAttendanceData.push(dummyObj);
        });

        try {
            let wb = XLSX.utils.book_new();
            let ws = XLSX.utils.json_to_sheet(srvcReqAttendanceData);
            XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
            XLSX.writeFile(wb, 'BookingFailed.xlsx');
            message.success('Downloaded');
        } catch (error) {
            setState({
                error: error,
            });
        }
    };
    const downloadSucessfullyBookedRequest = () => {
        let ColMeta = getColMeta();
        // let RowMeta = getRowMeta();
        let srvcReqAttendanceData = [];

        state.finalData.successfully_created.forEach((singleRowMeta) => {
            let dummyObj = {};
            ColMeta.forEach((singleColumns) => {
                dummyObj[singleColumns.title] = singleRowMeta[singleColumns.key]
                    ? singleRowMeta[singleColumns.key]
                    : '';
            });
            srvcReqAttendanceData.push(dummyObj);
        });

        try {
            let wb = XLSX.utils.book_new();
            let ws = XLSX.utils.json_to_sheet(srvcReqAttendanceData);
            XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
            XLSX.writeFile(wb, 'SuceesfullyCreated.xlsx');
            message.success('Downloaded');
        } catch (error) {
            setState({
                error: error,
            });
        }
    };
    const downloadUnVaildatedRequest = () => {
        let ColMeta = getColMeta();
        // let RowMeta = getRowMeta();
        let srvcReqAttendanceData = [];

        state.finalData.validation_errors.forEach((singleRowMeta) => {
            let dummyObj = {};
            ColMeta.forEach((singleColumns) => {
                dummyObj[singleColumns.title] = singleRowMeta[singleColumns.key]
                    ? singleRowMeta[singleColumns.key]
                    : '';
            });
            srvcReqAttendanceData.push(dummyObj);
        });

        try {
            let wb = XLSX.utils.book_new();
            let ws = XLSX.utils.json_to_sheet(srvcReqAttendanceData);
            XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
            XLSX.writeFile(wb, 'UnvaildRequest.xlsx');
            message.success('Downloaded');
        } catch (error) {
            setState({
                error: error,
            });
        }
    };

    const getPageNoOnTheBaseOfRow = (erroricRowsKey) => {
        return Math.ceil(erroricRowsKey / pageSize);
    };

    const getRowOnTheBaseOfPageSize = (erroricRowsKey) => {
        if (erroricRowsKey > pageSize) {
            return Math.ceil(erroricRowsKey % pageSize)
                ? Math.ceil(erroricRowsKey % pageSize)
                : pageSize;
        }
        return erroricRowsKey;
    };

    const getColumns = (columsArray) => {
        let firstRow = columsArray || state.rows[0];
        let columns = [];
        if (firstRow) {
            columns.push({
                title: 'Upload state',
                dataIndex: getUploadStateFieldIndex(),
                key: getUploadStateFieldIndex(),
                render: (text, record) => {
                    return (
                        <SingleRowFormFrBuilder
                            key={record.batch_data_row_key}
                            meta={getMeta()}
                            renderFullForm={renderFormsForRows}
                            initialValues={getFormDataForRow(record)}
                            onValidationError={(e) =>
                                handleFailedRow(record.batch_data_row_key)
                            }
                            onReadyToUpload={(formData) =>
                                handleReadyToUpload(
                                    formData,
                                    record.batch_data_row_key
                                )
                            }
                            isBulkAssignComp={isBulkAssignComp}
                        />
                    );
                },
            });
            !renderFormsForRows &&
                firstRow.map((singleCellValue, index) => {
                    let fieldMeta = getSingleColumFieldMeta(singleCellValue);
                    let singleColumnData = {
                        title: singleCellValue,
                        dataIndex: index,
                        key: index,
                        className: fieldMeta.required
                            ? 'gx-bg-amber-light'
                            : '',
                    };
                    if (fieldMeta.widget == 'date-picker') {
                        singleColumnData.render = (text, record) => {
                            if (text != '') {
                                let value = ExcelDateToJSDate(text);
                                if (value) {
                                    value = moment(value);
                                    value = value.format('MMM-DD-YYYY');
                                } else {
                                    value = moment(value);
                                }
                                return value;
                            }
                            return text;
                        };
                    }
                    columns.push(singleColumnData);
                });
        }
        return columns;
    };

    // Additional button handlers
    const handleButton1Click = () => {
        message.info('Button 1 clicked!');
        downloadSucessfullyBookedRequest();
        // Add your custom logic here
    };

    const handleButton2Click = () => {
        message.info('Button 2 clicked!');
        downloadFailedToBookRequest();
        // Add your custom logic here
    };

    const handleButton3Click = () => {
        message.info('Button 3 clicked!');
        downloadUnVaildatedRequest();
        // Add your custom logic here
    };

    const showUploadResp = () => {
        // show three cards with icon and count and download button in horizontal layout
        return (
            <div>
                <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
                    {/* Successfully Uploaded */}
                    <div
                        style={{
                            backgroundColor: '#f6ffed',
                            border: '1px solid #b7eb8f',
                            borderRadius: '8px',
                            padding: '16px',
                            flex: '1',
                            minWidth: '200px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '12px',
                        }}
                    >
                        <CheckCircleFilled
                            style={{
                                fontSize: '24px',
                                color: '#52c41a',
                            }}
                        />
                        <div style={{ flex: 1 }}>
                            <div
                                style={{
                                    fontWeight: '600',
                                    color: '#52c41a',
                                    fontSize: '14px',
                                    marginBottom: '4px',
                                }}
                            >
                                Successfully Uploaded
                            </div>
                            <div
                                style={{
                                    fontSize: '12px',
                                    color: '#666',
                                    marginBottom: '8px',
                                }}
                            >
                                Count:{' '}
                                {state.finalData.successfully_created.length}
                            </div>
                            <Button
                                type="primary"
                                size="small"
                                onClick={handleButton1Click}
                                style={{
                                    backgroundColor: '#52c41a',
                                    borderColor: '#52c41a',
                                    fontSize: '12px',
                                    height: '24px',
                                }}
                            >
                                Download
                            </Button>
                        </div>
                    </div>

                    {/* Failed to Book */}
                    <div
                        style={{
                            backgroundColor: '#fff2f0',
                            border: '1px solid #ffccc7',
                            borderRadius: '8px',
                            padding: '16px',
                            flex: '1',
                            minWidth: '200px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '12px',
                        }}
                    >
                        <CloseCircleFilled
                            style={{
                                fontSize: '24px',
                                color: '#ff4d4f',
                            }}
                        />
                        <div style={{ flex: 1 }}>
                            <div
                                style={{
                                    fontWeight: '600',
                                    color: '#ff4d4f',
                                    fontSize: '14px',
                                    marginBottom: '4px',
                                }}
                            >
                                Failed to Booked
                            </div>
                            <div
                                style={{
                                    fontSize: '12px',
                                    color: '#666',
                                    marginBottom: '8px',
                                }}
                            >
                                Count: {state.finalData.failed_to_book.length}
                            </div>
                            <Button
                                danger
                                size="small"
                                onClick={handleButton2Click}
                                style={{
                                    fontSize: '12px',
                                    height: '24px',
                                }}
                            >
                                Download
                            </Button>
                        </div>
                    </div>

                    {/* Error in Request */}
                    <div
                        style={{
                            backgroundColor: '#fffbf0',
                            border: '1px solid #ffe58f',
                            borderRadius: '8px',
                            padding: '16px',
                            flex: '1',
                            minWidth: '200px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '12px',
                        }}
                    >
                        <ExclamationCircleFilled
                            style={{
                                fontSize: '24px',
                                color: '#faad14',
                            }}
                        />
                        <div style={{ flex: 1 }}>
                            <div
                                style={{
                                    fontWeight: '600',
                                    color: '#faad14',
                                    fontSize: '14px',
                                    marginBottom: '4px',
                                }}
                            >
                                Error in Request
                            </div>
                            <div
                                style={{
                                    fontSize: '12px',
                                    color: '#666',
                                    marginBottom: '8px',
                                }}
                            >
                                Count:{' '}
                                {state.finalData.validation_errors.length}
                            </div>
                            <Button
                                size="small"
                                onClick={handleButton3Click}
                                style={{
                                    borderColor: '#faad14',
                                    color: '#faad14',
                                    fontSize: '12px',
                                    height: '24px',
                                }}
                            >
                                Download
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    // Render method
    const {
        bulkUploadInProgress,
        bulkUploadProgress,
        uploadComplete,
        clearFileInput,
        errorInfile,
        erroricRows,
        majorErrors,
        uploadResp,
        fileCheckingInProg,
    } = state;

    const clearInputFileProp = clearFileInput ? { value: '' } : {};
    const readyToUpload = isReadyToUpload();
    const dataProtoData = getDataProto();

    return (
        <div>
            {demoMode && (
                <Alert message="Running in demo mode" type="warning"></Alert>
            )}
            {debugMode && (
                <div>
                    <Input
                        placeholder="Output JSON will show here"
                        value={JSON.stringify(getPostData())}
                    />
                </div>
            )}
            {!update && (
                <div className="">
                    <h5>
                        Accepted columns
                        <small>( * are mandatory )</small>
                        <ExcelFile
                            filename={
                                update
                                    ? 'Bulk_update_template'
                                    : 'Bulk_upload_template'
                            }
                            element={
                                <Button
                                    icon={<DownloadOutlined />}
                                    className="gx-mb-0"
                                    type="link"
                                >
                                    Template
                                </Button>
                            }
                        >
                            <ExcelSheet data={[]} name="Sheet 1">
                                {dataProtoData
                                    .filter((fieldMeta) => fieldMeta.label)
                                    .map((fieldMeta) => (
                                        <ExcelColumn
                                            key={fieldMeta.key}
                                            label={fieldMeta.label}
                                            value={fieldMeta.label}
                                            style={{
                                                fill: {
                                                    patternType: 'solid',
                                                    fgColor: {
                                                        rgb: 'FFFF0000',
                                                    },
                                                },
                                            }}
                                        />
                                    ))}
                            </ExcelSheet>
                        </ExcelFile>
                    </h5>
                    <div
                        style={{
                            overflowX: 'auto',
                        }}
                    >
                        <table className="gx-w-100">
                            <tbody>
                                <tr>
                                    {dataProtoData.map(
                                        (singleFieldMeta, index) =>
                                            singleFieldMeta.label && (
                                                <td
                                                    key={index}
                                                    className={
                                                        singleFieldMeta.required
                                                            ? 'gx-bg-amber-light'
                                                            : ''
                                                    }
                                                    style={{
                                                        border: '2px solid #dddddd',
                                                        textAlign: 'center',
                                                        padding: '8px',
                                                        fontSize: '0.8rem',
                                                    }}
                                                >
                                                    {singleFieldMeta.label}
                                                    {singleFieldMeta.options && (
                                                        <td className="gx-d-flex gx-justify-content-center gx-align-items-center">
                                                            <Button
                                                                type="link"
                                                                onClick={() =>
                                                                    showSelectFieldOptionsModal(
                                                                        singleFieldMeta.key
                                                                    )
                                                                }
                                                                size="small"
                                                            >
                                                                View Data
                                                            </Button>
                                                            <Modal
                                                                title={`${singleFieldMeta.label} list`}
                                                                visible={
                                                                    state
                                                                        .multipleFieldDataModals[
                                                                        singleFieldMeta
                                                                            .key
                                                                    ]
                                                                }
                                                                footer={null}
                                                                onCancel={() =>
                                                                    hideSelectFieldOptionsModal(
                                                                        singleFieldMeta.key
                                                                    )
                                                                }
                                                            >
                                                                <p>
                                                                    {singleFieldMeta.options.map(
                                                                        (
                                                                            option,
                                                                            index
                                                                        ) => (
                                                                            <Paragraph
                                                                                className="gx-mb-1"
                                                                                key={
                                                                                    index
                                                                                }
                                                                                copyable={{
                                                                                    text: `${option?.label}`,
                                                                                }}
                                                                            >
                                                                                {index +
                                                                                    1}

                                                                                .{' '}
                                                                                {
                                                                                    option?.label
                                                                                }
                                                                            </Paragraph>
                                                                        )
                                                                    )}
                                                                </p>
                                                            </Modal>
                                                        </td>
                                                    )}
                                                </td>
                                            )
                                    )}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <hr></hr>
                </div>
            )}
            <div className="gx-mt-3">
                <h3>Upload an excel</h3>
                <div className="gx-d-flex">
                    <div style={{ width: '30px' }}>
                        <FileIcon
                            extension=".xlsx"
                            {...defaultStyles['xlsx']}
                        />
                    </div>
                    <input
                        type="file"
                        {...clearInputFileProp}
                        onClick={(e) =>
                            setState((prev) => ({
                                ...prev,
                                fileCheckingInProg: true,
                            }))
                        }
                        disabled={bulkUploadInProgress || uploadComplete}
                        onChange={(e) => {
                            handleFileChange(e);
                        }}
                        style={{ padding: '10px' }}
                        data-testid="upload-excel-file"
                    />
                </div>

                {bulkUploadProgress > 0 && bulkUploadProgress < 100 && (
                    <>
                        <Spin></Spin>
                        <br></br>
                        Checking file..
                        <Progress
                            className="gx-ml-2"
                            percent={bulkUploadProgress}
                        />
                    </>
                )}
                {maxRows > 0 && !update && (
                    <p>**Max {maxRows} rows supported at a time</p>
                )}
                {!update && (
                    <div>
                        <p>
                            **If a column has data condition and if data is out
                            of possible values then it will be treated as
                            empty{' '}
                        </p>
                        <p className="gx-text-orange">
                            **Date format should be YYYY-MM-DD{' '}
                        </p>
                    </div>
                )}
                {timeFormatMsg && !update && (
                    <p className="gx-text-red">
                        **Time format should be HH:MMAM/PM (Eg: 09:30AM){' '}
                    </p>
                )}
                {readyToUpload && (
                    <>
                        {majorErrors.length > 0 && (
                            <>
                                {majorErrors.map((singleLine, index) => (
                                    <p className="gx-text-red" key={index}>
                                        {singleLine}
                                    </p>
                                ))}
                            </>
                        )}
                        {Object.keys(majorErroricRowsKeysRef.current).length >
                            0 && (
                            <>
                                {Object.keys(
                                    majorErroricRowsKeysRef.current
                                ).map((majorErroricRowsKey) => (
                                    <p
                                        className="gx-text-red"
                                        key={majorErroricRowsKey}
                                    >
                                        error on page{' '}
                                        {getPageNoOnTheBaseOfRow(
                                            majorErroricRowsKey
                                        )}{' '}
                                        in row{' '}
                                        {getRowOnTheBaseOfPageSize(
                                            majorErroricRowsKey
                                        )}
                                    </p>
                                ))}
                            </>
                        )}
                        {errorInfile && erroricRows > 0 && (
                            <>
                                {Object.keys(erroricRowsKeysRef.current).map(
                                    (singleErroricRowsKey) => (
                                        <p
                                            className="gx-text-red"
                                            key={singleErroricRowsKey}
                                        >
                                            error on page{' '}
                                            {getPageNoOnTheBaseOfRow(
                                                singleErroricRowsKey
                                            )}{' '}
                                            in row{' '}
                                            {getRowOnTheBaseOfPageSize(
                                                singleErroricRowsKey
                                            )}
                                        </p>
                                    )
                                )}
                            </>
                        )}
                        <Button
                            type="primary"
                            disabled={
                                !readyToUpload ||
                                bulkUploadInProgress ||
                                uploadComplete ||
                                errorInfile ||
                                fileCheckingInProg
                            }
                            onClick={(e) => {
                                onStartBulkCreation(e);
                            }}
                        >
                            {update
                                ? 'Start bulk updation'
                                : 'Start bulk creation'}
                        </Button>

                        <Button onClick={(e) => handleClear()} type="link">
                            Reset/Clear
                        </Button>
                        <br></br>
                        {uploadComplete && (
                            <Alert
                                type="info"
                                message={
                                    <div>
                                        <h2>Bulk upload results</h2>
                                        <br></br>
                                        <p
                                            style={
                                                props.errorHasLineBreaks
                                                    ? {
                                                          whiteSpace:
                                                              'pre-wrap',
                                                      }
                                                    : {}
                                            }
                                        >
                                            {showUploadResp()}
                                        </p>
                                    </div>
                                }
                            />
                        )}
                        {bulkUploadInProgress && (
                            <>
                                <Spin></Spin>
                                <br></br>
                                Upload in progress..
                            </>
                        )}

                        {renderFormsForRows ? (
                            <List
                                itemLayout="itemLayout"
                                size="large"
                                bordered
                                pagination={{
                                    onChange: (page) => {
                                        console.log(page);
                                        setState((prev) => ({
                                            ...prev,
                                            currentPage: page,
                                        }));
                                    },
                                    pageSize: 10,
                                    current: state.currentPage,
                                }}
                                dataSource={getData()}
                                renderItem={(item) => (
                                    <List.Item key={item.batch_data_row_key}>
                                        <SingleRowFormFrBuilder
                                            meta={getMeta()}
                                            renderFullForm
                                            initialValues={getFormDataForRow(
                                                item
                                            )}
                                            onValidationError={(e) =>
                                                handleFailedRow(
                                                    item.batch_data_row_key
                                                )
                                            }
                                            onReadyToUpload={(formData) =>
                                                handleReadyToUpload(
                                                    formData,
                                                    item.batch_data_row_key
                                                )
                                            }
                                            isBulkAssignComp={isBulkAssignComp}
                                        />
                                    </List.Item>
                                )}
                            />
                        ) : (
                            <Table
                                className="gx-table-responsive"
                                columns={getColumns()}
                                dataSource={getData()}
                                pagination={{
                                    onChange: (page) => {
                                        console.log(page);
                                        setState((prev) => ({
                                            ...prev,
                                            currentPage: page,
                                        }));
                                    },
                                    pageSize: 10,
                                    current: state.currentPage,
                                }}
                                scroll={{ x: true }}
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default BulkUploaderV2;
