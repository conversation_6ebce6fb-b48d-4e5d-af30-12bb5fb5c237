// Test script for bulk booking API
const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust based on your server configuration
const API_ENDPOINT = '/api/v1/services/modify_booking';

// Sample test data
const testData = {
    selected_date: '2024-12-04', // Tomorrow's date as mentioned in requirements
    batch_data: [
        {
            pincode: '110001',
            cust_full_name: '<PERSON>',
            cust_mobile: '9876543210',
            cust_line_0: 'Flat 101',
            cust_line_1: 'ABC Apartments',
            cust_line_2: 'Sector 1',
            cust_city: 'Delhi',
            cust_state: 'Delhi',
            request_description: 'AC repair service',
            request_priority: 'High'
        },
        {
            pincode: '110002',
            cust_full_name: '<PERSON>',
            cust_mobile: '9876543211',
            cust_line_0: 'Flat 202',
            cust_line_1: 'XYZ Complex',
            cust_line_2: 'Sector 2',
            cust_city: 'Delhi',
            cust_state: 'Delhi',
            request_description: 'Plumbing service',
            request_priority: 'Medium'
        },
        {
            pincode: '110003',
            cust_full_name: '<PERSON>',
            cust_mobile: '9876543212',
            cust_line_0: 'House 303',
            cust_line_1: 'PQR Colony',
            cust_line_2: 'Sector 3',
            cust_city: 'Delhi',
            cust_state: 'Delhi',
            request_description: 'Electrical work',
            request_priority: 'Low'
        },
        {
            pincode: '12345', // Invalid pincode to test validation
            cust_full_name: 'Invalid User',
            cust_mobile: '9876543213',
            request_description: 'Test invalid pincode'
        },
        {
            pincode: '110004',
            cust_full_name: 'Alice Brown',
            cust_mobile: '9876543214',
            cust_line_0: 'Flat 404',
            cust_line_1: 'LMN Heights',
            cust_line_2: 'Sector 4',
            cust_city: 'Delhi',
            cust_state: 'Delhi',
            request_description: 'Cleaning service',
            request_priority: 'High'
        }
    ]
};

// Function to test the bulk booking API
async function testBulkBookingAPI() {
    try {
        console.log('🚀 Testing Bulk Booking API...');
        console.log('📅 Selected Date:', testData.selected_date);
        console.log('📦 Total Requests:', testData.batch_data.length);
        console.log('');

        // Make API call
        const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, testData, {
            headers: {
                'Content-Type': 'application/json',
                // Add authentication headers if required
                // 'Authorization': 'Bearer your-token-here'
            }
        });

        console.log('✅ API Response Status:', response.status);
        console.log('📊 Response Data:');
        console.log(JSON.stringify(response.data, null, 2));

        // Parse and display results
        if (response.data.status && response.data.data) {
            const { summary, successfully_created, failed_to_book, validation_errors } = response.data.data;
            
            console.log('\n📈 SUMMARY:');
            console.log(`Total Requests: ${summary.total_requests}`);
            console.log(`✅ Successfully Created: ${summary.successfully_created || 0}`);
            console.log(`❌ Failed to Book: ${summary.failed_to_book || 0}`);
            console.log(`⚠️  Validation Errors: ${summary.validation_errors || 0}`);

            if (successfully_created && successfully_created.length > 0) {
                console.log('\n✅ SUCCESSFULLY CREATED:');
                successfully_created.forEach((item, index) => {
                    console.log(`${index + 1}. Service Request ID: ${item.srvc_req_id}, Pincode: ${item.pincode}`);
                });
            }

            if (failed_to_book && failed_to_book.length > 0) {
                console.log('\n❌ FAILED TO BOOK (No Slots Available):');
                failed_to_book.forEach((item, index) => {
                    console.log(`${index + 1}. Pincode: ${item.pincode}, Error: ${item.error}`);
                });
            }

            if (validation_errors && validation_errors.length > 0) {
                console.log('\n⚠️  VALIDATION ERRORS:');
                validation_errors.forEach((item, index) => {
                    console.log(`${index + 1}. Pincode: ${item.pincode}, Error: ${item.error}`);
                });
            }
        }

    } catch (error) {
        console.error('❌ API Test Failed:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.error('Error:', error.message);
        }
    }
}

// Function to generate Excel download data (dummy implementation)
function generateExcelData(apiResponse) {
    if (!apiResponse.data) return;

    const { successfully_created, failed_to_book, validation_errors } = apiResponse.data;
    
    console.log('\n📊 EXCEL DOWNLOAD DATA STRUCTURE:');
    
    // Successfully Created Sheet
    if (successfully_created && successfully_created.length > 0) {
        console.log('\n📋 Sheet 1: Successfully Created');
        console.log('Columns: Service Request ID, Customer Name, Pincode, Booking Details, Capacity ID');
        successfully_created.forEach(item => {
            console.log(`${item.srvc_req_id}, ${item.request_data.cust_full_name}, ${item.pincode}, ${JSON.stringify(item.booking_details)}, ${item.capacity_id}`);
        });
    }

    // Failed to Book Sheet
    if (failed_to_book && failed_to_book.length > 0) {
        console.log('\n📋 Sheet 2: Failed to Book (No Slots Available)');
        console.log('Columns: Customer Name, Pincode, Error, Selected Date');
        failed_to_book.forEach(item => {
            console.log(`${item.request_data.cust_full_name}, ${item.pincode}, ${item.error}, ${item.selected_date}`);
        });
    }

    // Validation Errors Sheet
    if (validation_errors && validation_errors.length > 0) {
        console.log('\n📋 Sheet 3: Validation Errors');
        console.log('Columns: Customer Name, Pincode, Error');
        validation_errors.forEach(item => {
            console.log(`${item.request_data.cust_full_name || 'N/A'}, ${item.pincode}, ${item.error}`);
        });
    }
}

// Run the test
if (require.main === module) {
    console.log('🧪 Bulk Booking API Test Suite');
    console.log('================================');
    testBulkBookingAPI().then(() => {
        console.log('\n✨ Test completed!');
    });
}

module.exports = {
    testBulkBookingAPI,
    generateExcelData,
    testData
};
