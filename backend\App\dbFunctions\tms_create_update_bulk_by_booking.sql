CREATE OR REPLACE FUNCTION public.tms_create_update_bulk_by_booking(form_data_ text)
RETURNS json
LANGUAGE plpgsql
AS $function$
declare
    status boolean default false;
    message text default 'Internal error';
    data_ json;
    form_data_json jsonb;
    batch_data jsonb;
    org_id_ integer;
    usr_id_ text;
    ip_address_ text;
    user_agent_ text;
    srvc_type_id_ integer;
    selected_date_ date;
    
    -- Result arrays
    successfully_created jsonb[] default '{}';
    failed_to_book jsonb[] default '{}';
    validation_errors jsonb[] default '{}';
    
    -- Loop variables
    single_request jsonb;
    srvc_req_pincode text;
    validation_result json;
    booking_result json;
    capacity_result json;
    srvc_req_id bigint;
    
    -- Dummy data for testing
    dummy_capacity_available boolean default true;
    
begin
    -- Parse input form data
    form_data_json = form_data_::jsonb;
    
    -- Extract metadata
    org_id_ = (form_data_json->>'org_id')::integer;
    usr_id_ = form_data_json->>'usr_id';
    ip_address_ = form_data_json->>'ip_address';
    user_agent_ = form_data_json->>'user_agent';
    srvc_type_id_ = (form_data_json->>'srvc_type_id')::integer;
    selected_date_ = (form_data_json->batch_data->>'bulk_booking_day')::date;
    batch_data = form_data_json->'batch_data';
    
    -- Validate required fields
    if org_id_ is null or usr_id_ is null or srvc_type_id_ is null then
        return json_build_object(
            'status', false,
            'message', 'Missing required fields: org_id, usr_id, or srvc_type_id',
            'data', null
        );
    end if;
    
    if batch_data is null or jsonb_array_length(batch_data) = 0 then
        return json_build_object(
            'status', false,
            'message', 'No batch data provided',
            'data', null
        );
    end if;
    
    -- Process each request in batch
    for i in 0..jsonb_array_length(batch_data) - 1 loop
        single_request = batch_data->i;
        srvc_req_pincode = single_request->>'pincode';
        
        begin
            -- Step 1: Validate service request data
            validation_result = tms_hlpr_srvc_req_validation(single_request::text, org_id_);
            
            if not (validation_result->>'status')::boolean then
                -- Add to validation errors
                validation_errors = validation_errors || jsonb_build_object(
                    'request_data', single_request,
                    'error', validation_result->>'message',
                    'pincode', srvc_req_pincode
                );
                continue;
            end if;
            
            -- Step 2: Check if pincode falls in service area (dummy check for now)
            -- if srvc_req_pincode is null or length(srvc_req_pincode) < 6 then
            --     validation_errors = validation_errors || jsonb_build_object(
            --         'request_data', single_request,
            --         'error', 'Invalid pincode format',
            --         'pincode', srvc_req_pincode
            --     );
            --     continue;
            -- end if;
            
            -- Step 3: Check capacity availability (dummy implementation)
            -- In real implementation, this would call capacity API
            dummy_capacity_available = (random() > 0.3); -- 70% success rate for demo
            
            if not dummy_capacity_available then
                -- Add to failed bookings due to no slots
                failed_to_book = failed_to_book || jsonb_build_object(
                    'request_data', single_request,
                    'error', 'No slots available for selected date',
                    'pincode', srvc_req_pincode,
                    'selected_date', selected_date_
                );
                continue;
            end if;
            
            -- Step 4: Create service request (dummy implementation)
            -- In real implementation, this would call tms_create_service_request
            srvc_req_id = floor(random() * 1000000 + 1000000)::bigint; -- Generate dummy ID
            
            -- Step 5: Handle booking details (dummy implementation)
            -- In real implementation, this would call booking-related functions
            booking_result = json_build_object(
                'status', true,
                'message', 'Booking created successfully',
                'capacity_id', floor(random() * 10000 + 1000)::bigint,
                'booking_details', json_build_object(
                    'selected_date', selected_date_,
                    'time_slot', '09:00-12:00',
                    'provider_id', floor(random() * 100 + 1)::integer
                )
            );
            
            -- Add to successfully created
            successfully_created = successfully_created || jsonb_build_object(
                'srvc_req_id', srvc_req_id,
                'request_data', single_request,
                'pincode', srvc_req_pincode,
                'booking_details', booking_result->'booking_details',
                'capacity_id', booking_result->'capacity_id'
            );
            
        exception when others then
            -- Add to validation errors for any unexpected errors
            validation_errors = validation_errors || jsonb_build_object(
                'request_data', single_request,
                'error', 'Unexpected error: ' || SQLERRM,
                'pincode', srvc_req_pincode
            );
        end;
    end loop;
    
    -- Prepare response data
    data_ = json_build_object(
        'summary', json_build_object(
            'total_requests', jsonb_array_length(batch_data),
            'successfully_created', array_length(successfully_created, 1),
            'failed_to_book', array_length(failed_to_book, 1),
            'validation_errors', array_length(validation_errors, 1)
        ),
        'successfully_created', successfully_created,
        'failed_to_book', failed_to_book,
        'validation_errors', validation_errors,
        'selected_date', selected_date_
    );
    
    status = true;
    message = 'Bulk booking processing completed';
    
    return json_build_object(
        'status', status,
        'message', message,
        'data', data_
    );
    
exception when others then
    return json_build_object(
        'status', false,
        'message', 'Database error: ' || SQLERRM,
        'data', null
    );
end;
$function$;
