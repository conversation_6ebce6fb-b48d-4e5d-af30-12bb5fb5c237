# Bulk Booking API Documentation

## Overview
The Bulk Booking API allows you to create multiple service requests with booking functionality in a single API call. This API processes each request, validates the data, checks capacity availability, and returns three categorized lists of results.

## API Endpoint
```
POST /api/v1/services/modify_booking
```

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer <your-token> (if authentication is required)
```

### Request Body
```json
{
  "selected_date": "2024-12-04",
  "batch_data": [
    {
      "pincode": "110001",
      "cust_full_name": "John Doe",
      "cust_mobile": "9876543210",
      "cust_line_0": "Flat 101",
      "cust_line_1": "ABC Apartments",
      "cust_line_2": "Sector 1",
      "cust_city": "Delhi",
      "cust_state": "Delhi",
      "request_description": "AC repair service",
      "request_priority": "High"
    },
    {
      "pincode": "110002",
      "cust_full_name": "<PERSON>",
      "cust_mobile": "9876543211",
      "request_description": "Plumbing service"
    }
  ]
}
```

### Required Fields
- `selected_date`: The date for which booking slots should be checked (YYYY-MM-DD format)
- `batch_data`: Array of service request objects
- `batch_data[].pincode`: Customer pincode (required for each request)

### Optional Fields (per request)
- `cust_full_name`: Customer full name
- `cust_mobile`: Customer mobile number
- `cust_line_0`: Flat/House number
- `cust_line_1`: Building/Apartment name
- `cust_line_2`: Street/Area
- `cust_city`: City
- `cust_state`: State
- `request_description`: Service description
- `request_priority`: Priority level (High/Medium/Low)

## Response Format

### Success Response
```json
{
  "status": true,
  "message": "Bulk booking processing completed",
  "data": {
    "summary": {
      "total_requests": 5,
      "successfully_created": 3,
      "failed_to_book": 1,
      "validation_errors": 1
    },
    "successfully_created": [
      {
        "srvc_req_id": 1234567,
        "request_data": { /* original request data */ },
        "pincode": "110001",
        "booking_details": {
          "selected_date": "2024-12-04",
          "time_slot": "09:00-12:00",
          "provider_id": 42
        },
        "capacity_id": 5678
      }
    ],
    "failed_to_book": [
      {
        "request_data": { /* original request data */ },
        "error": "No slots available for selected date",
        "pincode": "110002",
        "selected_date": "2024-12-04"
      }
    ],
    "validation_errors": [
      {
        "request_data": { /* original request data */ },
        "error": "Invalid pincode format",
        "pincode": "12345"
      }
    ],
    "selected_date": "2024-12-04"
  }
}
```

### Error Response
```json
{
  "status": false,
  "message": "Invalid batch data. Expected non-empty array.",
  "data": null
}
```

## Response Categories

### 1. Successfully Created
Requests that passed validation, had available capacity, and were successfully created with booking details.

**Fields:**
- `srvc_req_id`: Generated service request ID
- `request_data`: Original request data
- `pincode`: Customer pincode
- `booking_details`: Booking information including time slot and provider
- `capacity_id`: Assigned capacity ID

### 2. Failed to Book (No Slots Available)
Requests that passed validation but couldn't be booked due to unavailable capacity/slots.

**Fields:**
- `request_data`: Original request data
- `error`: Error message explaining why booking failed
- `pincode`: Customer pincode
- `selected_date`: The date for which booking was attempted

### 3. Validation Errors
Requests that failed basic validation checks.

**Fields:**
- `request_data`: Original request data
- `error`: Validation error message
- `pincode`: Customer pincode (if available)

## Excel Download Structure

The API response can be used to generate Excel files with three sheets:

### Sheet 1: Successfully Created
| Service Request ID | Customer Name | Pincode | Booking Details | Capacity ID |
|-------------------|---------------|---------|-----------------|-------------|
| 1234567 | John Doe | 110001 | 09:00-12:00 | 5678 |

### Sheet 2: Failed to Book
| Customer Name | Pincode | Error | Selected Date |
|---------------|---------|-------|---------------|
| Jane Smith | 110002 | No slots available | 2024-12-04 |

### Sheet 3: Validation Errors
| Customer Name | Pincode | Error |
|---------------|---------|-------|
| Invalid User | 12345 | Invalid pincode format |

## Testing

Run the test script to verify the API functionality:

```bash
node backend/test_bulk_booking.js
```

## Implementation Notes

### Current Implementation (Dummy)
- Uses random success/failure rates for demonstration
- Generates dummy service request IDs and capacity IDs
- Basic pincode validation (length check)

### Production Implementation Requirements
1. **Real Validation**: Integrate with `tms_hlpr_srvc_req_validation` function
2. **Capacity Integration**: Connect with actual capacity/booking system APIs
3. **Service Request Creation**: Use `tms_create_service_request` function
4. **Booking Management**: Implement real booking slot assignment
5. **Pincode Verification**: Check against actual service area coverage
6. **Provider Assignment**: Integrate with provider selection logic

### Database Function
The API calls `tms_create_update_bulk_by_booking` database function which:
- Validates input data
- Processes each request in the batch
- Categorizes results into three arrays
- Returns structured response for Excel generation

## Error Handling

The API handles various error scenarios:
- Missing required fields
- Invalid batch data format
- Database connection errors
- Validation failures
- Capacity unavailability
- Unexpected processing errors

Each error is categorized appropriately and included in the response for proper handling and reporting.
