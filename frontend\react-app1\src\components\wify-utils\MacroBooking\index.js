import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Spin, Button, Select } from 'antd';
import {
    CalendarOutlined,
    LeftOutlined,
    RightOutlined,
} from '@ant-design/icons';
import BulkUploader from '../BulkUploader'; // Assuming BulkUploader is a separate component
import BulkUploaderV2 from '../BulkUploaderV2'; // New functional component
import Progress from 'antd/es/progress';
import { FaRegCalendar } from 'react-icons/fa';
import { BsChevronLeft, BsChevronRight } from 'react-icons/bs';
import RemoteSourceSelect from '../../../components/wify-utils/RemoteSourceSelect';
import http_utils from '../../../util/http_utils';
import './index.css';

const MacroBookingComponent = ({
    selectedPrvdr,
    onDataModified,
    submitUrl,
    dataProto,
    orgSettingsData,
}) => {
    const [loading, setLoading] = useState(false);
    const [selectedDay, setSelectedDay] = useState(null);
    const [startIndex, setStartIndex] = useState(0);
    const [scrollIndex, setScrollIndex] = useState(0);
    const [daysData, setDaysData] = useState([]);
    const [error, setError] = useState(undefined);

    // Initialize component
    useEffect(() => {
        // Component initialization
        initviewData();
    }, []);

    const initviewData = () => {
        if (loading) return;
        setLoading(true);
        setDaysData([]);
        setError(undefined);
        let params = {
            org_id: orgSettingsData?.org_id,
            vertical_id: orgSettingsData?.vertical_id,
        };
        const onComplete = (resp) => {
            // console.log('Availability slots response:', resp);
            setDaysData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            //console.error('Error fetching availability slots:', error);
            setDaysData([]);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/booking/city-capacity-data',
            params,
            onComplete,
            onError
        );
    };

    const cardsToShow = 4;

    const handleDaySelect = (dayId) => {
        setSelectedDay(dayId);
    };

    const handleNextScroll = () => {
        setScrollIndex(scrollIndex + 1);
    };

    const handlePrevScroll = () => {
        setScrollIndex(scrollIndex - 1);
    };
    const getEnhancedDataProto = () => {
        const enhancedProto = [...dataProto];

        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }
        if (selectedDay) {
            enhancedProto.selectedDay = selectedDay; // Add selected day data
        }

        return enhancedProto;
    };
    return (
        <div style={{ marginTop: '16px' }}>
            <>
                {loading ? (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                        <Spin size="large" />
                    </div>
                ) : (
                    <>
                        {/* Horizontal Scrollable Row */}
                        <div
                            style={{
                                alignItems: 'center',
                                position: 'relative',
                            }}
                        >
                            {/* Left Arrow Button */}
                            <Button
                                icon={<BsChevronLeft />}
                                className="wy-sb-nav-btn wy-sb-prev-nav-btn"
                                onClick={handlePrevScroll}
                                disabled={scrollIndex === 0}
                                shape="circle"
                                style={{
                                    position: 'absolute',
                                    left: '0',
                                    top: '50%',
                                    transform: 'translateY(-84%)',
                                    zIndex: 1,
                                }}
                            />

                            {/* Row of cards */}
                            <Row
                                gutter={[16, 16]}
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                }}
                            >
                                {daysData
                                    .slice(
                                        scrollIndex,
                                        scrollIndex + cardsToShow
                                    )
                                    .map((day) => (
                                        <Col xs={24} sm={6} key={day.id}>
                                            <Card
                                                hoverable={day.is_available}
                                                className={`day-card ${
                                                    selectedDay === day.id
                                                        ? 'selected'
                                                        : ''
                                                } ${day.is_available ? '' : 'unavailable'}`}
                                                onClick={() => {
                                                    if (day.is_available) {
                                                        handleDaySelect(day.dayDate);
                                                    }
                                                }}
                                                style={{
                                                    border:
                                                        selectedDay === day.id
                                                            ? '2px solid #1890ff'
                                                            : '1px solid #d9d9d9',
                                                    cursor: day.is_available
                                                        ? 'pointer'
                                                        : 'not-allowed',
                                                    borderRadius: '8px',
                                                    boxShadow:
                                                        '0 2px 8px rgba(0,0,0,0.05)',
                                                    opacity: day.is_available
                                                        ? 1
                                                        : 0.5,
                                                }}
                                            >
                                                <div
                                                    style={{
                                                        textAlign: 'left',
                                                        padding: '10px',
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            alignItems:
                                                                'center',
                                                            marginBottom: '8px',
                                                        }}
                                                    >
                                                        <CalendarOutlined
                                                            style={{
                                                                fontSize:
                                                                    '20px',
                                                                color: '#1890ff',
                                                                marginRight:
                                                                    '6px',
                                                            }}
                                                        />
                                                        <div>
                                                            <div
                                                                style={{
                                                                    fontWeight:
                                                                        '600',
                                                                    fontSize:
                                                                        '15px',
                                                                }}
                                                            >
                                                                {day.name}
                                                            </div>
                                                            <div
                                                                style={{
                                                                    fontSize:
                                                                        '12px',
                                                                    color: '#666',
                                                                }}
                                                            >
                                                                {day.date}
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="gx-mb-1 gx-fs-sm">
                                                        <div>
                                                            Total Slots:{' '}
                                                            {day.totalSlots}
                                                        </div>
                                                        <div>
                                                            Available:{' '}
                                                            {day.available}/
                                                            {day.capacity}
                                                        </div>
                                                    </div>

                                                    {/* Progress bar */}
                                                    <div
                                                        style={{
                                                            width: '100%',
                                                            height: '8px',
                                                            backgroundColor:
                                                                '#eee',
                                                            borderRadius: '4px',
                                                            marginTop: '6px',
                                                        }}
                                                    >
                                                        <div
                                                            style={{
                                                                height: '100%',
                                                                width: `${(day.availableCapacity / day.totalCapacity) * 100}%`,
                                                                backgroundColor:
                                                                    '#1890ff',
                                                                borderRadius:
                                                                    '4px',
                                                            }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            </Card>
                                        </Col>
                                    ))}
                            </Row>

                            {/* Right Arrow Button */}
                            <Button
                                icon={<BsChevronRight />}
                                className="wy-sb-nav-btn wy-sb-next-nav-btn"
                                onClick={handleNextScroll}
                                shape="circle"
                                disabled={
                                    scrollIndex + cardsToShow >= daysData.length
                                }
                                style={{
                                    position: 'absolute',
                                    right: '0',
                                    top: '50%',
                                    transform: 'translateY(-84%)',
                                    zIndex: 1,
                                }}
                            />
                        </div>

                        {/* Bulk uploader V2 */}
                        {selectedDay && (
                            <div style={{ marginTop: '16px' }}>
                                <BulkUploaderV2
                                    onDataModified={onDataModified}
                                    submitUrl={submitUrl}
                                    dataProto={getEnhancedDataProto()}
                                    orgSettingsData={orgSettingsData}
                                />
                            </div>
                        )}
                    </>
                )}
            </>
        </div>
    );
};

export default MacroBookingComponent;
