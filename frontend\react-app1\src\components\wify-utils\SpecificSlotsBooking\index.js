import React, { useState, useEffect } from 'react';
import { <PERSON>, Col, Card, Spin, Button, Typography } from 'antd';
import {
    CalendarOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    CheckCircleFilled,
    CloseCircleFilled,
} from '@ant-design/icons';
import BulkUploader from '../BulkUploader';
import RemoteSourceSelect from '../../../components/wify-utils/RemoteSourceSelect';
import http_utils from '../../../util/http_utils';
import './index.css';
import Progress from 'antd/es/progress';
import { BsChevronLeft, BsChevronRight } from 'react-icons/bs';
import BulkUploaderV2 from '../BulkUploaderV2';

const { Title, Text } = Typography;

// Add styles for specific slot booking cards
const styles = `
.wy-specific-slot-day-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;
}

.wy-specific-slot-day-header {
    background: #f0f8ff;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wy-specific-slot-day-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.wy-specific-slot-day-stats {
    font-size: 14px;
    color: #666;
}

.wy-specific-slot-time-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.wy-specific-slot-time-item:hover {
    background-color: #f8f9fa;
}

.wy-specific-slot-time-item:last-child {
    border-bottom: none;
}

.wy-specific-slot-time-item.selected {
    background-color: #e6f7ff;
    border-left: 4px solid #1890ff;
}

.wy-specific-slot-time-text {
    margin-left: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.wy-specific-slot-status-icon {
    font-size: 20px;
}

.wy-specific-slot-status-available {
    color: #52c41a;
}

.wy-specific-slot-status-unavailable {
    color: #ff4d4f;
}

.wy-specific-slot-status-partial {
    color: #1890ff;
}
`;

// Inject styles
if (typeof document !== 'undefined') {
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerText = styles;
    document.head.appendChild(styleSheet);
}

const SpecificSlotsBookingComponent = ({
    selectedPrvdr,
    onDataModified,
    submitUrl,
    dataProto,
    orgSettingsData,
}) => {
    const [loading, setLoading] = useState(false);
    const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
    const [daysData, setDaysData] = useState([]);
    const [slotsLoading, setSlotsLoading] = useState(false);
    const [selectedDay, setSelectedDay] = useState(null);
    const [showSingleDay, setShowSingleDay] = useState(false);
    const [error, setError] = useState(undefined);
    const [startIndex, setStartIndex] = useState(0);
    const [scrollIndex, setScrollIndex] = useState(0);

    useEffect(() => {
        initviewData();
    }, []);

    const initviewData = () => {
        if (loading) return;
        setLoading(true);
        setDaysData([]);
        setError(undefined);
        let params = {
            org_id: orgSettingsData?.org_id,
            vertical_id: orgSettingsData?.vertical_id,
        };
        const onComplete = (resp) => {
            // console.log('Availability slots response:', resp);
            setDaysData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            //console.error('Error fetching availability slots:', error);
            setDaysData([]);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/booking/city-slots-data',
            params,
            onComplete,
            onError
        );
    };

    // Handle time slot selection
    const handleTimeSlotSelect = (dayId, slotId) => {
        const selectedSlot = {
            dayId,
            slotId,
        };
        setSelectedTimeSlot(selectedSlot);

        // Find and set the selected day
        const day = daysData.find((d) => d.id === dayId);
        if (day) {
            setSelectedDay(day);
            setShowSingleDay(true);
        }
    };

    // Handle view all days
    const handleViewAllDays = () => {
        setShowSingleDay(false);
        setSelectedDay(null);
        setSelectedTimeSlot(null);
    };

    // Get status icon based on availability
    const getStatusIcon = (slot) => {
        const availableRatio = slot.availableCapacity / slot.totalCapacity;

        if (availableRatio === 0) {
            return (
                <CloseCircleOutlined className="wy-specific-slot-status-icon wy-specific-slot-status-unavailable" />
            );
        } else if (availableRatio === 1) {
            return (
                <CheckCircleOutlined className="wy-specific-slot-status-icon wy-specific-slot-status-available" />
            );
        } else {
            return (
                <CheckCircleOutlined className="wy-specific-slot-status-icon wy-specific-slot-status-partial" />
            );
        }
    };

    // Render time slots for a specific day in new UI format
    const renderTimeSlots = (day) => {
        if (!day.timeSlots || day.timeSlots.length === 0) {
            return (
                <div
                    style={{
                        textAlign: 'center',
                        padding: '15px',
                        color: '#999',
                    }}
                >
                    No time slots available
                </div>
            );
        }

        return (
            <div>
                {day.timeSlots.map((slot) => {
                    const isSelected =
                        selectedTimeSlot?.dayId === day.id &&
                        selectedTimeSlot?.slotId === slot.id;

                    return (
                        <div
                            key={slot.id}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                fontSize: '14px',
                                marginBottom: '4px',
                            }}
                        >
                            <span style={{ marginRight: '8px' }}>
                                {slot.available ? (
                                    <CheckCircleFilled
                                        style={{ color: '#1890ff' }}
                                    />
                                ) : (
                                    <CloseCircleFilled
                                        style={{ color: '#f5222d' }}
                                    />
                                )}
                            </span>
                            <span>{slot.timeRange}</span>
                        </div>
                    );
                })}
            </div>
        );
    };

    // Render single day view with new UI
    const renderSingleDayView = () => {
        if (!selectedDay) return null;

        const totalSlots = selectedDay.timeSlots?.length || 0;
        const availableSlots =
            selectedDay.timeSlots?.filter((slot) => slot.availableCapacity > 0)
                .length || 0;

        return (
            <div>
                {/* Header with View All Days button */}
                <div className="gx-p-3 wy-br-10 bg-white gx-card gx-d-flex gx-justify-content-between gx-align-items-center">
                    <p className="gx-text-black gx-fs-lg gx-mb-0">
                        Showing slots for {selectedDay.date}
                    </p>
                    <p
                        className="gx-text-primary gx-fs-sm wy-cursor-pointer gx-mb-0"
                        onClick={handleViewAllDays}
                    >
                        View All Days
                    </p>
                </div>

                {/* Single day card with new UI */}
                <Card className="wy-specific-slot-day-card">
                    <div className="wy-specific-slot-day-header">
                        <div className="wy-specific-slot-day-title">
                            <CalendarOutlined
                                style={{ marginRight: '8px', color: '#1890ff' }}
                            />
                            {selectedDay.name}
                            <br />
                            <span
                                style={{
                                    fontSize: '14px',
                                    fontWeight: 'normal',
                                    color: '#666',
                                }}
                            >
                                {selectedDay.date}
                            </span>
                        </div>
                        <div className="wy-specific-slot-day-stats">
                            <div>Total Slots: {totalSlots}</div>
                            <div>
                                Available: {availableSlots}/{totalSlots}
                            </div>
                        </div>
                    </div>
                    {renderTimeSlots(selectedDay)}
                </Card>
            </div>
        );
    };

    // Render day cards with new UI format
    const renderDayCards = () => {
        if (!daysData || daysData.length === 0) {
            return (
                <div
                    style={{
                        textAlign: 'center',
                        padding: '40px',
                        color: '#999',
                    }}
                >
                    Please select a city to view available slots
                </div>
            );
        }

        return daysData.map((day) => {
            const totalSlots = day.timeSlots?.length || 0;
            const availableSlots =
                day.timeSlots?.filter((slot) => slot.availableCapacity > 0)
                    .length || 0;

            return (
                <Card key={day.id} className="wy-specific-slot-day-card">
                    <div className="wy-specific-slot-day-header">
                        <div className="wy-specific-slot-day-title">
                            <CalendarOutlined
                                style={{ marginRight: '8px', color: '#1890ff' }}
                            />
                            {day.name}
                            <br />
                            <span
                                style={{
                                    fontSize: '14px',
                                    fontWeight: 'normal',
                                    color: '#666',
                                }}
                            >
                                {day.date}
                            </span>
                        </div>
                        <div className="wy-specific-slot-day-stats">
                            <div>Total Slots: {totalSlots}</div>
                            <div>
                                Available: {availableSlots}/{totalSlots}
                            </div>
                        </div>
                    </div>
                    {renderTimeSlots(day)}
                </Card>
            );
        });
    };
    const cardsToShow = 4;

    const handleDaySelect = (dayId) => {
        setSelectedDay(dayId);
    };

    const handleNextScroll = () => {
        setScrollIndex(scrollIndex + 1);
    };

    const handlePrevScroll = () => {
        setScrollIndex(scrollIndex - 1);
    };

    const getEnhancedDataProto = () => {
        const enhancedProto = [...dataProto];

        if (selectedPrvdr) {
            enhancedProto.selectedPrvdr = selectedPrvdr;
        }
        if (selectedDay) {
            enhancedProto.selectedDay = selectedDay.dayDate; // Add selected day data
        }

        return enhancedProto;
    };

    return (
        <div style={{ marginTop: '16px' }}>
            <>
                {loading ? (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                        <Spin size="large" />
                    </div>
                ) : (
                    <>
                        {/* Horizontal Scrollable Row */}
                        <div
                            style={{
                                alignItems: 'center',
                                position: 'relative',
                            }}
                        >
                            {/* Left Arrow Button */}
                            <Button
                                icon={<BsChevronLeft />}
                                className="wy-sb-nav-btn wy-sb-prev-nav-btn"
                                onClick={handlePrevScroll}
                                disabled={scrollIndex === 0}
                                shape="circle"
                                style={{
                                    position: 'absolute',
                                    left: '0',
                                    top: '50%',
                                    transform: 'translateY(-84%)',
                                    zIndex: 1,
                                }}
                            />

                            {/* Row of cards */}
                            <Row
                                gutter={[16, 16]}
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                }}
                            >
                                {daysData
                                    .slice(
                                        scrollIndex,
                                        scrollIndex + cardsToShow
                                    )
                                    .map((day) => (
                                        <Col xs={24} sm={6} key={day.id}>
                                            <Card
                                                hoverable={day.is_available}
                                                className={`day-card ${
                                                    selectedDay === day.id
                                                        ? 'selected'
                                                        : ''
                                                } ${day.is_available ? '' : 'unavailable'}`}
                                                onClick={() => {
                                                    if (day.is_available) {
                                                        handleDaySelect(day);
                                                    }
                                                }}
                                                style={{
                                                    border:
                                                        selectedDay === day.id
                                                            ? '2px solid #1890ff'
                                                            : '1px solid #d9d9d9',
                                                    cursor: day.is_available
                                                        ? 'pointer'
                                                        : 'not-allowed',
                                                    borderRadius: '8px',
                                                    boxShadow:
                                                        '0 2px 8px rgba(0,0,0,0.05)',
                                                    opacity: day.is_available
                                                        ? 1
                                                        : 0.5,
                                                }}
                                            >
                                                <div
                                                    style={{
                                                        textAlign: 'left',
                                                        padding: '10px',
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            alignItems:
                                                                'center',
                                                            marginBottom: '8px',
                                                        }}
                                                    >
                                                        <CalendarOutlined
                                                            style={{
                                                                fontSize:
                                                                    '20px',
                                                                color: '#1890ff',
                                                                marginRight:
                                                                    '6px',
                                                            }}
                                                        />
                                                        <div>
                                                            <div
                                                                style={{
                                                                    fontWeight:
                                                                        '600',
                                                                    fontSize:
                                                                        '15px',
                                                                }}
                                                            >
                                                                {day.name}
                                                            </div>
                                                            <div
                                                                style={{
                                                                    fontSize:
                                                                        '12px',
                                                                    color: '#666',
                                                                }}
                                                            >
                                                                {day.date}
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="gx-mb-1 gx-fs-sm">
                                                        <div>
                                                            Total Slots:{' '}
                                                            {day.totalSlots}
                                                        </div>
                                                        <div>
                                                            Available:{' '}
                                                            {day.available}/
                                                            {day.capacity}
                                                        </div>
                                                    </div>

                                                    {/* Progress bar */}
                                                    <div
                                                        className="gx-mb-3"
                                                        style={{
                                                            width: '100%',
                                                            height: '8px',
                                                            backgroundColor:
                                                                '#eee',
                                                            borderRadius: '4px',
                                                            marginTop: '6px',
                                                        }}
                                                    >
                                                        <div
                                                            style={{
                                                                height: '100%',
                                                                width: `${(day.availableCapacity / day.totalCapacity) * 100}%`,
                                                                backgroundColor:
                                                                    '#1890ff',
                                                                borderRadius:
                                                                    '4px',
                                                            }}
                                                        ></div>
                                                    </div>
                                                    {renderTimeSlots(day)}
                                                </div>
                                            </Card>
                                        </Col>
                                    ))}
                            </Row>

                            {/* Right Arrow Button */}
                            <Button
                                icon={<BsChevronRight />}
                                className="wy-sb-nav-btn wy-sb-next-nav-btn"
                                onClick={handleNextScroll}
                                shape="circle"
                                disabled={
                                    scrollIndex + cardsToShow >= daysData.length
                                }
                                style={{
                                    position: 'absolute',
                                    right: '0',
                                    top: '50%',
                                    transform: 'translateY(-84%)',
                                    zIndex: 1,
                                }}
                            />
                        </div>

                        {/* Bulk uploader V2 */}
                        {selectedDay && (
                            <div style={{ marginTop: '16px' }}>
                                <BulkUploaderV2
                                    onDataModified={onDataModified}
                                    submitUrl={submitUrl}
                                    dataProto={getEnhancedDataProto()}
                                    orgSettingsData={orgSettingsData}
                                    selectedSlotData={
                                        selectedDay?.timeSlots || []
                                    }
                                />
                            </div>
                        )}
                    </>
                )}
            </>
        </div>
    );
};

export default SpecificSlotsBookingComponent;
