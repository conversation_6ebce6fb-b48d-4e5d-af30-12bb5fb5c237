var express = require('express');
var router = express.Router();
var { getUserContextFrmReq } = require('../api_models/utils/authrizor');
var HttpStatus = require('http-status-codes');

router.get('/reqs-for-auto-assign', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getSrvcReqsFrGenAutoAssign(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.put(
    '/geolocation-update/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        model
            .updateMissingGeoLoc(req.params.srvc_req_id)
            .then((operationResp) => {
                res.status(HttpStatus.StatusCodes.OK).send('Success');
            });
    }
);

router.put(
    '/auto-assigned-authorities-refresh/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        model
            .updateAuthoritiesOfSrvcReqs(req.params.srvc_req_id)
            .then((operationResp) => {
                res.status(HttpStatus.StatusCodes.OK).send('Success');
            });
    }
);

router.put('/location-grp-update/:srvc_req_id', function (req, res, next) {
    const model = setParamsToModel(req);
    model
        .UpdateLocGrpIdsInSrvcReqs(req.params.srvc_req_id, req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/location-grp-name/:srvc_req_id', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getLocationGrpName(req.params.srvc_req_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post(
    '/exec-dynamic-form-logic/:srvc_type_id/:is_customer_access/:srvc_req_id?',
    (req, res, next) => {
        const model = setParamsToModel(req);
        // res.status(400).send(JSON.stringify({params:req.params,body:req.body}))
        model
            .callLambdaFnForExecutingDynamicFormLogic(
                req.params.is_customer_access,
                req.body
            )
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get(
    '/has_billing_approval_access/:srvc_req_id/:srvc_type_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        model
            .doesLoginUserHasDisApprovalAccess(req.query)
            .then((operationResp) => {
                res.send(operationResp);
            });
    }
);

router.get(
    '/deployment_daily_update/:srvc_req_id/:tab_name',
    function (req, res, next) {
        const model = setParamsToModel(req);
        let srvc_req_id = req.params.srvc_req_id;
        let tab_name = req.params.tab_name;
        model
            .getSrvcReqDeploymentDailyUpdateData(
                req.query,
                srvc_req_id,
                tab_name
            )
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get('/calendar_data/sbtsk_details', function (req, res, next) {
    const model = setParamsToModel(req);
    model
        .getSbtskDetailsFrCalendar(req.query, req.params.srvc_req_id)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/calendar_data/:srvc_req_id', function (req, res, next) {
    const model = setParamsToModel(req);
    model
        .getSrvcReqCalendarData(req.query, req.params.srvc_req_id)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.put(
    '/sync/calendar_data/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        // console.log(req.body);
        const model = setParamsToModel(req);
        model
            .syncCalenderDataWithSrvcReqFormData(req.body, req.params)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.post('/app_call', function (req, res, next) {
    const model = setParamsToModel(req);
    // console.log('const model = setParamsToModel(req)',const model = setParamsToModel(req))
    model.initiateConsumerCall(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/app_call', function (req, res, next) {
    //getAppCallStatus
    const model = setParamsToModel(req);
    model.getAppCallLog(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get(
    '/cust_access_overview_proto/:cust_org_id/:cust_srvc_type_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        var cust_org_id = req.params.cust_org_id;
        var cust_srvc_type_id = req.params.cust_srvc_type_id;
        model
            .getCustAccessOverviewProto(
                req.query,
                cust_org_id,
                cust_srvc_type_id
            )
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.put('/modify/:srvc_type_id/:entry_id', function (req, res, next) {
    // console.log(req.body);
    // res.send("Got call" + JSON.stringify(req.params));
    const model = setParamsToModel(req);
    var entry_id = req.params.entry_id ? req.params.entry_id : 0;
    model.createOrUpdate(req.body, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post(
    '/export/:srvc_type_id/:is_customer_access?',
    function (req, res, next) {
        // res.status('500').send("Got export request - " + JSON.stringify(req.body) );
        // // console.log(req.body);
        const model = setParamsToModel(req);
        model
            .exportServicesByEmail(req.body, req.params.is_customer_access)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.post(
    '/modify/:srvc_type_id/:is_customer_access?',
    function (req, res, next) {
        // console.log(req.body);
        let is_customer_access = req.params.is_customer_access;
        const model = setParamsToModel(req);
        if (req.body.batch_data) {
            // res.status(400).send("WIP Rxd data--> " + JSON.stringify(req.body));
            model
                .createOrUpdateBatch(req.body, is_customer_access)
                .then((operationResp) => {
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
            return;
        } else {
            model
                .createOrUpdate(req.body, 0, is_customer_access)
                .then((operationResp) => {
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
        }
    }
);

router.post(
    '/modify/:srvc_type_id/:is_customer_access/:is_bulk_update?',
    function (req, res, next) {
        const is_customer_access = req.params.is_customer_access;
        const is_bulk_update = req.params.is_bulk_update;
        const model = setParamsToModel(req);
        if (req.body.batch_data) {
            model
                .createOrUpdateBatch(
                    req.body,
                    is_customer_access,
                    0,
                    is_bulk_update
                )
                .then((operationResp) => {
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
        }
    }
);

router.post(
    '/modify/:lineitems/:srvc_type_id/:is_customer_access/:is_bulk_line_items_update?',
    function (req, res, next) {
        const is_customer_access = req.params.is_customer_access;
        const is_bulk_line_items_update = req.params.is_bulk_line_items_update;
        const lineItemKey = req.params.lineitems;
        const model = setParamsToModel(req);
        if (req.body.batch_data) {
            model
                .createOrUpdateSrvcLineItemsBatch(
                    req.body,
                    is_customer_access,
                    0,
                    is_bulk_line_items_update,
                    lineItemKey
                )
                .then((operationResp) => {
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
        }
    }
);

router.get(
    '/list/:srvc_type_id/:is_customer_access?',
    function (req, res, next) {
        // console.log(req.query);
        // res.send(JSON.stringify(req.query));
        const model = setParamsToModel(req);
        model
            .getAll(req.query, req.params.is_customer_access)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get(
    '/overview_proto/:srvc_type_id/:is_customer_access?',
    function (req, res, next) {
        const model = setParamsToModel(req);
        // res.send("Got call for service type id - " + req.params.srvc_type_id);
        // return;
        model
            .getOverviewProto(req.query, req.params.is_customer_access, true)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get('/proto/:srvc_type_id/:entry_id', function (req, res, next) {
    const model = setParamsToModel(req);
    var entry_id = req.params.entry_id;
    model.getSingleEntry(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/proto/:srvc_type_id', function (req, res, next) {
    const model = setParamsToModel(req);
    // res.send("Got call for service type id - " + req.params.srvc_type_id);
    // return;
    model.getViewDataFrForm(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/timeline/:srvc_type_id/:srvc_req_id', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getViewDataFrSrvcTimelineForm(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/cust_history', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getCustHistory(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get(
    '/sbtskdetails/attachment/:entry_id/:date',
    function (req, res, next) {
        const model = setParamsToModel(req);
        var entry_id = req.params.entry_id;
        var date = req.params.date;
        model
            .getAttachmentDataOfSubtask(req.query, entry_id, date)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get('/bulk_creation/proto', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getViewDataFrBulkCreationForm(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/bulk_creation/srvc_types/:entry_id', function (req, res, next) {
    const model = setParamsToModel(req);
    var entry_id = req.params.entry_id;
    model.getViewDataFrVertical(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/bulk_create_fr_vertical/:is_cust_req', function (req, res, next) {
    const model = setParamsToModel(req);
    const is_cust_req = req.params.is_cust_req;
    model
        .createOrUpdateBatchFrVertical(req.body, is_cust_req)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/auto_authority_assignment', function (req, res, next) {
    const model = setParamsToModel(req);
    model.autoAssignAuthoritiesToSrvcReq(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Validation middleware for bulk booking
const validateBulkBookingInput = (req, res, next) => {
    try {
        const { batch_data, selected_date } = req.body;

        // Check if batch_data exists and is an array
        if (
            !batch_data ||
            !Array.isArray(batch_data) ||
            batch_data.length === 0
        ) {
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: 'Invalid batch data. Expected non-empty array.',
                data: null,
            });
        }

        // Check if selected_date is provided
        if (!batch_data.bulk_booking_day) {
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: 'Selected date is required for bulk booking.',
                data: null,
            });
        }

        // Basic validation for each request in batch
        for (let i = 0; i < batch_data.length; i++) {
            const request = batch_data[i];
            if (!request.pincode) {
                return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                    status: false,
                    message: `Pincode is required for request at index ${i}`,
                    data: null,
                });
            }
        }

        next();
    } catch (error) {
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Validation error: ' + error.message,
            data: null,
        });
    }
};

router.post(
    '/modify_booking',
   // validateBulkBookingInput,
    function (req, res, next) {
        const model = setParamsToModel(req);
        model.createUpdateBulkByBooking(req.body).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);

router.get('/projects_overview_proto/:vertical_id?', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getProjectOverviewProto(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/projects_insight_list/:vertical_id?', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getProjectSrvcReqListByVerticalId(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/projects_export/:vertical_id?', function (req, res, next) {
    // res.status('500').send("Got export request - " + JSON.stringify(req.body) );
    // // console.log(req.body);
    const model = setParamsToModel(req);
    console.log('exportVisitMapByEmail', req.body);
    model.exportProjectsByEmail(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get(
    '/transition_overview_lifetime/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        model
            .getViewDataFrSrvcTransitionOverviewForm(req.query)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get('/global_search', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getGlobalSearchResults(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get(
    `/profit_and_loss_by_vertical/:org_id/:vertical_id/${process.env.UPDATE_PNL_DATA}`,
    function (req, res, next) {
        console.log('pnlcron debug link intiated', req.params);
        const model = setParamsToModel(req);
        model
            .getProfitAndLossByVertical(
                req.params.org_id,
                req.params.vertical_id,
                req.query
            )
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get(
    '/profit_and_loss/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        model.getProfitAndLossData(req.query).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);

router.get(
    '/project_profit_and_loss/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        const model = setParamsToModel(req);
        model.getProjectProfitAndLossData(req.query).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
);

router.put(
    '/profit_and_loss/update/:srvc_type_id/:srvc_req_id',
    function (req, res, next) {
        // console.log(req.body);
        // res.send("Got call" + JSON.stringify(req.params));
        const model = setParamsToModel(req);
        var entry_id = req.params.srvc_req_id ? req.params.srvc_req_id : 0;
        model
            .updateProfitAndLossOfSrvcReq(req.body, entry_id)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);
// router.put('/:entry_id', function(req, res, next) {
//   // console.log(req.body,req.params.entry_id);
//   // res.send("Got call");
//   setParamsToModel(req);
//   var entry_id = req.params.entry_id;
//   model.createOrUpdate(req.body,entry_id).then(
//       operationResp => {
//         res.status(operationResp.httpStatus).send(operationResp.resp);
//       }
//   );

// });

// router.get('/proto/:entry_id', function(req, res, next) {
//   // console.log(req.query,req.params);
//   // res.send("Got call");
//   setParamsToModel(req);
//   var entry_id = req.params.entry_id;
//   model.getSingleEntry(req.query,entry_id).then(
//       operationResp => {
//         res.status(operationResp.httpStatus).send(operationResp.resp);
//       }
//   );

// });

// router.get('/proto', function(req, res, next) {
//   // console.log(req);
//   // res.send("Got call");
//   setParamsToModel(req);
//   model.getViewDataFrForm(req.query).then(
//       operationResp => {
//         res.status(operationResp.httpStatus).send(operationResp.resp);
//       }
//   );

// });

// router.get('/', function(req, res, next) {
//     // console.log(req.query);
//     // res.send(JSON.stringify(req.query));
//     setParamsToModel(req);
//     model.getAll(req.query).then(
//       operationResp => {
//         res.status(operationResp.httpStatus).send(operationResp.resp);
//       }
//     );

// });

const setParamsToModel = (req) => {
    const model = require('../api_models/services_model').getInstance();
    model.database = req.app.get('db');
    model.databaseReplica = req.app.get('db_replica');
    model.databaseDump = req.app.get('db_dump');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    // Specific for service types
    model.srvc_type_id = req.params.srvc_type_id;
    model.srvc_req_id = req.params.srvc_req_id;
    model.vertical_id = req.params.vertical_id || req.query.vertical_id;
    return model.getFreshInstance(model);
};

module.exports = router;
